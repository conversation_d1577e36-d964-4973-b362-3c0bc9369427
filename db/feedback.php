<?php

error_reporting(0);

header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection.php';

/*
$serverName = "172.20.49.37"; //serverName\instanceName
$connectionInfo = array( "Database"=>"COMME", "UID"=>"comme", "PWD"=>"%user456");*/
$conn = sqlsrv_connect( $serverName, $connectionInfo);

if( $conn ) {	
			/*$staff_id          = $_POST['staff_id']; 						
			$fullname          = $_POST['fullname']; 						
			$comment           = $_POST['comment']; 						
			$region           = $_POST['region']; */

			$staff_id      = filter_var($_POST['staff_id'], FILTER_SANITIZE_STRING); 			
			$fullname      = filter_var($_POST['fullname'], FILTER_SANITIZE_STRING); 	
			$staff_email      = filter_var($_POST['staff_email'], FILTER_SANITIZE_STRING); 		
			$comment       = filter_var($_POST['comment'], FILTER_SANITIZE_STRING); 			
			$region        = filter_var($_POST['region'], FILTER_SANITIZE_STRING); 	

			$staff_id      = utf8_decode($staff_id);
			$fullname      = utf8_decode($fullname);
			$staff_email      = utf8_decode($staff_email);
			$comment       = utf8_decode($comment);
			$region        = utf8_decode($region);			
		
	
			$sql = "INSERT INTO [FEEDBACK] ( Staff_id,Staff_Name,Comment,region,status) VALUES 
					('$staff_id','$fullname','$comment','$region','OPEN')";
			
		
			$stmt = sqlsrv_query( $conn, $sql );
			
			if( $stmt === false ) {
				//die( print_r( sqlsrv_errors(), true));
				echo $result = json_encode(array('data' => 'FAILED'));	

			}else {
				echo $result = json_encode(array('data' => 'SUCCESS'));	

				//Send Email Address
				sendemail_feedback($fullname, $staff_email, $comment);

			}

}
else{
		echo "Connection could not be established.<br />";
		//die( print_r( sqlsrv_errors(), true));
	}


function sendemail_feedback($fullname, $staff_email, $comment) {

	require_once '../email/swift_required.php';

	$transport = Swift_SmtpTransport::newInstance('*********** 25', 25);
	$mailer = Swift_Mailer::newInstance($transport);
	$message = Swift_Message::newInstance('Feedback Query')
	->setSubject('eComme Feedback Query')
	->setFrom(array('<EMAIL>'))
	->setTo(array($staff_email))
	->setCc(array('<EMAIL>','<EMAIL>','<EMAIL>'))
	->setBody(
	'<html>' .
	' <head></head>' .
	' <body>' .
	' Dear '.$fullname.',<br><br> '.
	' Your Comment/Suggestion : <b> '.$comment.'</b><br><br>'.
	'You may reply this with this trail email.<br><br>We’re so happy to hear from you! Thank you for your valuable feedback.'.
	'<br><br><br>' .
	'<i><strong>DISCLAIMER</strong> : This E-mail and any files transmitted with it is intended only for the use of the recipients named above and may contain confidential information. If you are not the intended recipient be aware that any disclosure, copying, distribution or use of the contents of this information is strictly prohibited. The Email is and will remain the property and confidential information of Telekom Malaysia Berhad (known as TM) or one of its related companies ("TM").<i>'.
	'</body>' .
	'</html>',
	'text/html' // Mark the content-type as HTML
	)
	;
	//Send the message
	$result = $mailer->send($message);
	
	//echo "SUCCESS";



}


?>