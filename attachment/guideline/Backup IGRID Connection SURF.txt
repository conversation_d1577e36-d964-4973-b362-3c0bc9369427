<?php

error_reporting(0);
header('Content-Type: application/json;charset=utf-8'); //json header
include 'connection_server.php';
date_default_timezone_set("Asia/Kuala_Lumpur");

if (!$conn) echo $msg = 1;
else if ($_POST['select']==1) {
	
	$adServer = "ldaps://10.45.236.28";	
	$ldap = ldap_connect($adServer,636);
	ldap_set_option($ldap, LDAP_OPT_PROTOCOL_VERSION, 3);
	ldap_set_option($ldap, LDAP_OPT_REFERRALS, 0);
	$ldap_user   = "cn=surfldapadmin, ou=serviceAccount, o=Telekom";
	$ldap_pass   = "t67Ntdbk";
	
	$myusername = strtolower($_POST['username']);
	$mypassword = $_POST['password'];
	
	//Verify Username First in DB
	$sql = "SELECT * FROM dbo.surfuser where staff_id = '".$myusername."'";
	$params = array();
	$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
	$result = sqlsrv_query($conn, $sql , $params, $options);
	$count = sqlsrv_num_rows($result);
	$row = sqlsrv_fetch_array($result);	
	
	if($ldapbind = @ldap_bind($ldap, $ldap_user, $ldap_pass)) {

		if ($count == 1) {
		
			//Check Current Account Lock or Not
			if ($row['login_fail'] == 6) {
				
				$last_login = date_format($row['last_login'], 'Y-m-d H:i');
				//Count 1 Hour to reset
				$sqlfailed = "SELECT DATEADD(HOUR,1,'".$last_login."') AS \"DATEDIFF HOURS\" from dbo.surfuser";
				$params = array();
				$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
				$result = sqlsrv_query($conn, $sqlfailed , $params, $options);
				$count = sqlsrv_num_rows($result);
				$row = sqlsrv_fetch_array($result);
				
				$date = date('Y-m-d H:i');
				$timesejam = date_format($row['DATEDIFF HOURS'], 'Y-m-d H:i');
				$timestamp_login = strtotime($date);
				$timestamp_addonehour = strtotime($timesejam);			
				
				//If Time Account Lock after 1 Hour
				if ( $timestamp_login > $timestamp_addonehour) {
					
					$resource = @ldap_bind($ldap, "cn=$myusername, ou=users, o=data","$mypassword");
					if($resource){
						
						$result = ldap_search($ldap,'ou=users, o=data',"cn=$myusername");
						$info = ldap_get_entries($ldap, $result);
						$fullname=$info[0]['sn'][0];
						$dateupdate = date('Y-m-d H:i:s'); //Returns IST
						$staff_id = $row["staff_id"];
						$workgroup = $row["workgroup"];
						$department = $row["department"];
						$privilege = $row["privilege"];
						
						$sql = "UPDATE dbo.surfuser SET last_login = '$dateupdate', login_fail = 1 WHERE staff_id = '$myusername'";
						$params = array();
						$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
						$result = sqlsrv_query($conn, $sql , $params, $options);
						
						session_start();
						$_SESSION["fullname"] = $fullname;
						$_SESSION["staff_id"] = $staff_id;
						$_SESSION["workgroup"] = $workgroup;
						$_SESSION["department"] = $department;
						$_SESSION["privilege"] = $privilege;
						
						$msg = 3;

					}
					else {
						
						$dateupdate = date('Y-m-d H:i:s'); //Returns IST
						$sql = "UPDATE dbo.surfuser SET last_login = '$dateupdate', login_fail = 2 WHERE staff_id = '$myusername'";
						$params = array();
						$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
						$result = sqlsrv_query($conn, $sql , $params, $options);
						$msg = "4_4";
					}					
				}
				else {
					$msg = 6;				
				}			
			}
			else {
				
				$resource = @ldap_bind($ldap, "cn=$myusername, ou=users, o=data","$mypassword");
				if($resource){
					
					$result = ldap_search($ldap,'ou=users, o=data',"cn=$myusername");
					$info = ldap_get_entries($ldap, $result);
					$fullname=$info[0]['sn'][0];
					$dateupdate = date('Y-m-d H:i:s'); //Returns IST
					$staff_id = $row["staff_id"];
					$workgroup = $row["workgroup"];
					$department = $row["department"];
					$privilege = $row["privilege"];
					
					$sql = "UPDATE dbo.surfuser SET last_login = '$dateupdate', login_fail = 1 WHERE staff_id = '$myusername'";
					$params = array();
					$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
					$result = sqlsrv_query($conn, $sql , $params, $options);
					
					session_start();
					$_SESSION["fullname"] = $fullname;
					$_SESSION["staff_id"] = $staff_id;
					$_SESSION["workgroup"] = $workgroup;
					$_SESSION["department"] = $department;
					$_SESSION["privilege"] = $privilege;
					
					$msg = 3;
					
				}
				else {
				
					$login_fail = $row['login_fail'] + 1;
					$dateupdate = date('Y-m-d H:i:s'); //Returns IST
					$sql = "UPDATE dbo.surfuser SET last_login = '$dateupdate', login_fail = '$login_fail' WHERE staff_id = '$myusername'";
					$params = array();
					$options =  array( "Scrollable" => SQLSRV_CURSOR_KEYSET );
					$result = sqlsrv_query($conn, $sql , $params, $options);

					if ($login_fail == 2) {

						$msg = "4_4";

					} else if ($login_fail == 3) {

						$msg = "4_3";

					} else if ($login_fail == 4) {

						$msg = "4_2";

					} else if ($login_fail == 5) {

						$msg = "4_1";

					} else if ($login_fail == 6) {

						$msg = 6;

					}
				}				
			}
		}
		else {	
			$msg = 2; 
		}
	}
	else {
		$msg = 5; 
	}
	echo $msg;
	//1 - Failed to connect to database
	//2 - User is not registered in SURF
	//3 - Successfully login
	//4_4 - Incorrect GEMS id/password - 4
	//4_3 - Incorrect GEMS id/password - 3
	//4_2 - Incorrect GEMS id/password - 2
	//4_1 - Incorrect GEMS id/password - 1
	//5 - Unable to connect to LDAP server
	//6 - Account Lock
}
else if($_POST['select']==2){
	session_start(); 
	session_unset(); 
	session_destroy();
	echo "1";
}

?>
