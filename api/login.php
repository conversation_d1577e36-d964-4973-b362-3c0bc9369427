<?php
	//error_reporting(0);
	
	require_once 'db/headers.php';	
	require_once 'db/userAuth.php';
	
	$auth = new userAuth();
	
	$postdata = file_get_contents("php://input");
  	
	if ($postdata) {
		
        $request = json_decode($postdata);
		$username = $request->username;
		$password = $request->password;
		$msg = $auth->authUser($username, $password);
		
		echo json_encode(array($msg));
		
    } else {
        echo json_encode(array("status"=>"Not called properly"));
    }

 ?>