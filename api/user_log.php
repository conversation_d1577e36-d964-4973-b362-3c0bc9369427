<?php
 
	require_once('db/headers.php');
	require_once('db/userAuth.php'); 
	require_once('db/ez_sql_core.php');
	require_once('db/ez_sql_sqlsrv.php');
	
	$auth = new userAuth();
	
	$postdata = file_get_contents("php://input");
  	
	if ($postdata) {
		
        $request = json_decode($postdata);
		$token = $request->token;
		$validtoken = $auth->validtoken($token,null);
		
		if($validtoken){
			$data = $db->get_results("SELECT * FROM USERS_LOG");
			echo json_encode(array("status"=>"true","data" => $data));
		} else {
			echo json_encode(array("token"=>"Token Invalid"));
		}
		
    } else {
        echo json_encode(array("status"=>"Not called properly"));
    }

 ?>