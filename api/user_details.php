<?php
 
	require_once('db/headers.php');
	require_once('db/userAuth.php'); 
	
	$auth = new userAuth();
	
	$postdata = file_get_contents("php://input");  
  	
	if ($postdata) {
		
        $request = json_decode($postdata);
		$token = $request->token;
		if(isset($request->staffid)){$staffid = $request->staffid;}
		// check token validation
		$validtoken = $auth->validtoken($token,null);
		
		if($validtoken){
			if(isset($staffid)){
				$profiles = $auth->finduser($token,$staffid);
				echo json_encode(array("status"=>$validtoken,"profile"=>$profiles));
			} else {	
				$profiles = $auth->validtoken($token,"userdetails");
				echo json_encode(array("status"=>$validtoken,"profile"=>$profiles));
			}
		} else {
			//return header('HTTP/1.1 401 Unauthorized', true, 401);
			echo json_encode(array("status"=>"Token Invalid"));
		}
		
    } else {
        echo json_encode(array("status"=>"Not called properly"));
    }

 ?>